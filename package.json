{"name": "vue3_cli_default", "version": "0.0.0", "scripts": {"dev": "vite serve --mode development", "build": "vite build --base=/"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "autoprefixer": "^10.4.15", "axios": "^1.5.0", "element-plus": "^2.3.14", "pinia": "^2.1.7", "postcss-pxtorem": "^6.0.0", "rollup-plugin-visualizer": "^5.9.2", "sass": "^1.67.0", "sass-loader": "^13.3.2", "vant": "^4.8.7", "vue": "^3.2.8", "vue-router": "^4.2.4"}, "devDependencies": {"@vitejs/plugin-vue": "^1.6.0", "@vue/compiler-sfc": "^3.2.6", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.2", "vite": "^2.5.2", "vite-plugin-compression": "^0.5.1"}}