
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import {visualizer} from "rollup-plugin-visualizer"
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import viteCompression from "vite-plugin-compression";
export default defineConfig({
  publicPath: process.env.NODE_ENV === "production" ? '' : "",
  outputDir: 'dist',
  assetsDir: 'static',
  lintOnSave: true,
  productionSourceMap: false,
  plugins: [
      vue(),
      AutoImport({
        resolvers: [ElementPlusResolver()],
        imports:["vue","vue-router"],   //自动引入vue和router API
        dts:'src/auto-import.d.ts'    // 路径下自动生成文件夹存放全局指令
      }),
      Components({
        resolvers: [ElementPlusResolver()],
      }),
      visualizer({
        emitFile:false,
        file:"states.html",
        open:true
     }),
     viteCompression({
      verbose: true,    
      disable: false,
      threshold: 10240,  
      algorithm: 'gzip',
      ext: '.gz',
      })
  ],
  server: {
    host: '0.0.0.0',
    port: 8080,
    cors: true, // 默认启用并允许任何源
    open: true, // 在服务器启动时自动在浏览器中打开应用程序
    //反向代理配置，注意rewrite写法
    proxy: {
      "/api": {
        target: 'http://grove.cpzsyun.com:81/api/control/', //代理接口`   
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ""),
      },
    },
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src"),
      "comps": path.resolve(__dirname, "src/components"),
    }
  },
  
build:{
  rollupOptions:{
      output:{  //静态资源分类打包
          chunkFileNames: 'static/js/[name]-[hash].js',      
          entryFileNames: 'static/js/[name]-[hash].js',      
          assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
          manualChunks(id){ //静态资源拆分打包
              if (id.includes('node_modules')) {      
                  return id.toString().split('node_modules/')[1].split('/')[0].toString(); }
           }

      }
  }
}
})