<template>
  <div id="app">
    <keep-alive>
      <router-view v-if="$route.meta.keepAlive"></router-view>
    </keep-alive>
    <router-view v-if="!$route.meta.keepAlive"></router-view>
  </div>
</template>

<script>
import { debounce } from "@/util/debounce";
import { indexStore } from '@/store/index.js'
export default {
  name: 'App',
  data() {
      return {
      isPC: true, //控制显示登录dialog

      }
   },
  components: {
    
  },
  created(){
    this.getScreen()
  },
  mounted(){
    window.addEventListener('resize', () => debounce(this.getScreen(),500))
    // window.addEventListener('resize', () => this.getScreen())
  },
  methods:{
     //获取屏幕大小
     getScreen() { 
      const swidth = document.body.clientWidth
      if(swidth>900){
         this.isPC= true;
      }else{
         this.isPC= false;
      }
      const store = indexStore()
      store.isPC = this.isPC
    }

  }
}
</script>

<style scoped>
#app {
  background-color: #f5f5f5;
  
  width: 100%;
  height: 100%;
}
</style>
