import { createApp } from 'vue'
import router from './router/index'
// import ElementPlus from 'element-plus'
// import 'element-plus/dist/index.css'
// import * as ElementPlusIconsVue from '@element-plus/icons-vue'
// import  './rem.js'
import App from './App.vue'
import "@/styles/index.css"; // 引入初始化css
import Vant from 'vant';
import 'vant/lib/index.css';


import {createPinia} from 'pinia'
const store = createPinia()


const app =createApp(App)
// for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
//     app.component(key, component)
//   }
app.use(Vant)
// app.use(ElementPlus)
app.use(router)
app.use(store)
app.mount('#app')
