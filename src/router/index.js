import { createRouter, createWebHistory } from "vue-router";
const routes = [
  {
    path: "/",
    meta: {
      title: "新闻首页",
      menuShow: true,
    },
    component: () => import("@/view/index/index.vue"),
  },

  {
    path: "/index",
    meta: {
      title: "新闻首页",
      menuShow: true,
      routePath: "/index",
    },
    component: () => import("@/view/index/index.vue"),
  },
  {
    path: "/test",
    meta: {
      title: "新闻首页",
      menuShow: true,
      routePath: "/test",
    },
    component: () => import("@/view/test/test.vue"),
  },
  {
    path: "/company",
    meta: {
      title: "企业风采",
      menuShow: true,
      routePath: "/company",
    },
    component: () => import("@/view/company/company.vue"),
  },
];

const router = createRouter({
  mode: "history",
  history: createWebHistory(import.meta.env.VITE_BASE_PATH),
  routes,
});
router.beforeEach((to, from, next) => {
  window.scroll(0, 0);
  next();
});
export default router;
