import axios from "axios"
export  const request =(options)=> {
    return new Promise((resolve, reject) => {
        // create an axios instance
        const service = axios.create({
            baseURL:'http://grove.cpzsyun.com:81/api',
            timeout: 10000 
        })
        service.interceptors.request.use(
            (config) => {
                // let token = "eyJjcmVhdGV0aW1lIjoxNjk5NTI0NDk1ODA0LCJleHBpcmUiOjE2OTk2MTA4OTU4MDQsImFsZyI6IkhTMjU2In0=.eyJpZCI6IjQwMjg4MTRiODlhZTlkOTkwMThhYjA4MjJmZDgwMDJjIiwicGFnZSI6MCwiY29tcGFueUlkIjoiNDAyODgxNGI4OWFlOWQ5OTAxOGFiMDgyMmZkOTAwMmQiLCJ1c2VybmFtZSI6IjE4OTk4NzY5ODc2IiwicGFzc3dvcmQiOiI0Mjk3ZjQ0YjEzOTU1MjM1MjQ1YjI0OTczOTlkN2E5MyIsInBob25lIjoiMTg5OTg3Njk4NzYiLCJ1c2VyVHlwZSI6IkdPViIsImxvY2tlZCI6ZmFsc2V9.7d3c445b9563d09d9e7fd6cca3f76a15aa8e663a70255dd68106ba8fb2288411"
                // if (token) {
                //     config.headers['token'] = token
                // }
                return config
            },
            (error) => {
                console.log(error)
            }
        )
        service.interceptors.response.use(
            (response) => {
                if(response.data.code==105) ElementUI.Message.error(response.data.msg)
                return response.data
            }, (error) => {
                console.log(error.status)
                if(error.request.responseType == 'blob'){
                    return Promise.reject(error)
                }else{
                    return Promise.reject(error)
                }
            }
        )
        // 请求处理
        service(options)
            .then((res) => {
                resolve(res)
            })
            .catch((error) => {
                reject(error)
            })
    })
}
export default request