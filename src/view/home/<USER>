<template>
  <div class="home-container">
    <HomeWeb v-if="isPC" />
    <HomeMobile v-else />
  </div>
</template>

<script setup>
import { computed } from "vue";
import { indexStore } from "@/store/index.js";
import HomeWeb from "./HomeWeb.vue";
import HomeMobile from "./HomeMobile.vue";

// 计算属性：判断是否为PC端
const isPC = computed(() => {
  return indexStore().isPC;
});
</script>

<style scoped lang="scss">
.home-container {
  width: 100%;
  height: 100%;
}
</style>