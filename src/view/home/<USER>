<template>
  <div class="home-web">
    <!-- 轮播图 -->
    <van-swipe class="my-swipe" :autoplay="3000" indicator-color="white">
      <van-swipe-item v-for="(item, idx) in swipeListWeb" :key="idx">
        <img :src="item.imgUrl" alt="" srcset="" class="swipeImg" />
      </van-swipe-item>
    </van-swipe>

    <!-- 关于我们 -->
    <div class="about">
      <div class="title">关于我们</div>
      <div class="eglx">ABOUT US</div>
      <div class="detali">
        <img src="@/assets/images/web/about.png" alt="" srcset="" class="aboutImgWeb" />
        <div class="info">
          <div class="introduct">
            福尚云位于广州，福尚云专业向全国企业客户提供各类工会福利产品、礼赠品、商务用品、办公用品等。包括节日慰问品、电影票、生日蛋糕、优选购物、劳保用品、办公用品、礼赠品、企业工装定制等综合性服务。福尚云顺应市场发展趋势，整合各类资源，辅以技术创新，让客户可选多元化、高品质的产品及服务。
          </div>
          <div class="introduct">
            福尚云开发的"福尚云系统"可以为企业量身定制专属的福利管理综合平台，为企业员工提供无接触式福利发放。员工在福利平台中可以自主选择适合自己的福利，企业只需要制定福利投入预算即可。有效优化企业福利管理效率，降低企业管理者在福利发放中人力、物力的成本，提升员工归属感和满意度，满足员工自主的选择权力。
          </div>
        </div>
      </div>
    </div>

    <!-- 定制服务 -->
    <div class="fuwu">
      <div class="title">定制服务</div>
      <div class="eglx">CUSTOMIZED SERVICE</div>
      <div class="fwcontent">
        <div class="fwItem" v-for="(item, j) in dingZhiImg" :key="j">
          <img :src="item.imgUrl" alt="" />
          <div class="fwInfo">
            <div class="main">{{ item.title }}</div>
            <div class="second">{{ item.info }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 电影通兑 -->
    <div class="moviebg">
      <img src="@/assets/images/web/yingyuantongdui.jpg" alt="" />
    </div>

    <!-- 生日福利 -->
    <div class="shengri">
      <div class="title">生日福利</div>
      <div class="eglx">BIRTHDAY CAKE</div>
      <img src="@/assets/images/web/shengri2.jpg" alt="" />
    </div>

    <!-- 合作伙伴 -->
    <div class="hezuo">
      <div class="title">合作伙伴</div>
      <div class="eglx">COOPERATIVE PARTNER</div>
      <img src="@/assets/images/web/hezuo.jpg" alt="" />
    </div>

    <!-- 云福优选 -->
    <div class="youxuan">
      <div class="title">云福优选</div>
      <div class="eglx">YUNFU MALL</div>
      <div class="fwcontent">
        <div class="fwItem" v-for="(item, j) in youxuan" :key="j">
          <img :src="item.imgUrl" alt="" />
          <div class="fwInfo">
            <div class="main">{{ item.title }}</div>
            <div class="second">{{ item.info }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 平台优势 -->
    <div class="youshi">
      <img src="@/assets/images/web/wmdyx.png" alt="" />
    </div>

    <!-- 页脚 -->
    <div class="footer">
      <div class="left">
        <div class="forthe">版权所有 © fushangyunfu.com</div>
        <div class="forthe">广州福尚云科技研发有限公司</div>
        <div class="forthe">
          <a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备 </a>
          <a href="https://beian.miit.gov.cn/" target="_blank"> 2024197431号-2</a>
        </div>
        <div class="forthe">增值电信业务经营许可证：粤B2-20241521</div>
        <div><EMAIL></div>
      </div>
      <div class="right">
        <div class="kefu">
          <div class="biaoti">专属服务热线</div>
          <div class="info">周一至周日(09:00-22:00)</div>
          <div class="tel">************</div>
        </div>
        <div class="qrCode">
          <img src="@/assets/images/mobile/qrcode2.jpg" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import img2 from "@/assets/images/web/banner.png";
import img3 from "@/assets/images/web/dz1.jpg";
import img4 from "@/assets/images/web/dz2.jpg";
import img5 from "@/assets/images/web/dz3.jpg";
import img11 from "@/assets/images/web/yx1.jpg";
import img12 from "@/assets/images/web/yx2.jpg";
import img13 from "@/assets/images/web/yx3.jpg";
import img14 from "@/assets/images/web/yx4.jpg";
import img15 from "@/assets/images/web/yx5.jpg";
import img16 from "@/assets/images/web/yx6.jpg";
import img17 from "@/assets/images/web/yx7.jpg";
import img18 from "@/assets/images/web/yx8.jpg";
import img19 from "@/assets/images/web/yx9.jpg";
import img20 from "@/assets/images/web/yx10.jpg";

// 轮播图数据
const swipeListWeb = ref([{ imgUrl: img2, url: "" }]);

// 定制服务数据
const dingZhiImg = ref([
  {
    imgUrl: img3,
    title: "平台定制",
    info: "企业专属SAAS平台定制个性化企业定制服务",
  },
  {
    imgUrl: img4,
    title: "产品定制",
    info: "根据企业产品偏好定制专属福利产品方案",
  },
  {
    imgUrl: img5,
    title: "设计定制",
    info: "卡券、卡套、生日贺卡、公众号等专属定制",
  },
]);

// 云福优选数据
const youxuan = ref([
  { imgUrl: img11, title: "食品" },
  { imgUrl: img12, title: "洗护" },
  { imgUrl: img13, title: "居家" },
  { imgUrl: img14, title: "家电" },
  { imgUrl: img15, title: "餐厨" },
  { imgUrl: img16, title: "母婴" },
  { imgUrl: img17, title: "图书" },
  { imgUrl: img18, title: "办公" },
  { imgUrl: img19, title: "生鲜" },
  { imgUrl: img20, title: "票务" },
]);


</script>

<style scoped lang="scss">
.home-web {
  width: 100%;
  height: 100%;

  .my-swipe {
    width: 100%;
    .van-swipe-item {
      width: 100%;
      .swipeImg {
        width: 100%;
      }
    }
  }

  .title {
    width: 100%;
    text-align: center;
    color: #333;
    font-size: 30px;
  }

  .eglx {
    line-height: 28px;
    font-size: 16px;
    color: #777;
    text-align: center;
    margin-bottom: 20px;
  }

  .about {
    width: 100%;
    background-color: #f5f5f5;
    padding: 50px;

    .detali {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .aboutImgWeb {
        width: 340px;
        height: 250px;
        margin-right: 30px;
      }

      .info {
        width: 450px;
        text-indent: 2em;
        font-size: 14px;
        color: #333;
        line-height: 24px;
      }
    }
  }

  .fuwu {
    width: 100%;
    padding: 50px;

    .fwcontent {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      .fwItem {
        width: 340px;
        height: 240px;
        margin-right: 30px;
        position: relative;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          transition: 2s;
        }

        img:hover {
          transform: scale(1.2);
        }

        .fwInfo {
          width: 100%;
          height: 100%;
          position: absolute;
          top: 0;
          left: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          color: #fff;

          .main {
            width: 100%;
            font-size: 24px;
            text-align: center;
          }

          .second {
            width: 160px;
            font-size: 14px;
            text-align: center;
          }
        }
      }

      .fwItem:hover img {
        transform: scale(1.2);
      }
    }
  }

  .youxuan {
    width: 1500px;
    padding: 50px;
    margin: 0 auto;

    .fwcontent {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;

      .fwItem {
        width: 250px;
        height: 230px;
        margin: 0px 30px 30px 0;
        position: relative;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          transition: 2s;
        }

        img:hover {
          transform: scale(1.2);
        }

        .fwInfo {
          width: 100%;
          height: 100%;
          position: absolute;
          top: 0;
          left: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          color: #fff;

          .main {
            width: 100%;
            font-size: 24px;
            text-align: center;
          }

          .second {
            width: 160px;
            font-size: 14px;
            text-align: center;
          }
        }
      }

      .fwItem:hover img {
        transform: scale(1.2);
      }
    }
  }

  .shengri {
    width: 100%;
    padding: 50px;
    background: #f5f5f5;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    img {
      width: 1100px;
      height: 330px;
    }
  }

  .moviebg {
    width: 100%;

    img {
      width: 100%;
    }
  }

  .hezuo {
    width: 100%;
    padding: 50px;
    background: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    img {
      width: 1100px;
    }
  }

  .youshi {
    width: 100%;

    img {
      width: 100%;
    }
  }

  .footer {
    width: 100%;
    height: 120px;
    padding: 0px 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #333;
    font-size: 14px;
    box-sizing: border-box;
    margin-top: 50px;
    padding-bottom: 10px;

    .left {
      line-height: 24px;
    }

    .right {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .kefu {
        line-height: 24px;
        margin-right: 50px;

        .info {
          color: #666;
        }

        .tel {
          font-size: 24px;
        }
      }

      .qrCode {
        width: 90px;
        height: 90px;

        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}

a {
  text-decoration: underline;
  color: #4182ff;
}
</style>
