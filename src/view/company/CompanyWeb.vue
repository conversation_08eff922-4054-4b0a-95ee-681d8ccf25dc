<template>
  <div class="company-web">
    <!-- 顶部轮播图 -->
    <section class="hero-banner">
      <van-swipe
        class="banner-swipe"
        :autoplay="config.bannerSettings.autoplay"
        :indicator-color="config.bannerSettings.indicatorColor"
        :touchable="config.bannerSettings.touchable"
        :show-indicators="config.bannerSettings.showIndicators"
        :loop="config.bannerSettings.loop"
        :duration="config.bannerSettings.duration"
      >
        <van-swipe-item v-for="banner in config.banners" :key="banner.id">
          <img
            :src="banner.src"
            :alt="banner.alt"
            class="banner-image"
          />
        </van-swipe-item>
      </van-swipe>
    </section>

    <!-- 主要内容区域 -->
    <section class="main-content">
      <div class="container">
        <div class="content-layout">
          <!-- 左侧导航 -->
          <aside class="sidebar">
            <div class="sidebar-header">
              <h3>LATEST</h3>
              <h3>NEWS</h3>
              <div style="display: flex; align-items: center">
                <div class="divider"></div>
                <div class="dot"></div>
              </div>
              <h2>最新动态</h2>
            </div>
            <nav class="sidebar-nav">
              <div
                class="nav-item"
                :class="{ active: activeTab === item.key }"
                v-for="item in config.navItems"
                :key="item.key"
                @click="switchTab(item.key)"
              >
                {{ item.label }}
              </div>
            </nav>
          </aside>

          <!-- 右侧内容 -->
          <main class="content-area">
            <div class="news-list">
              <div
                class="news-item"
                v-for="(news, index) in currentNewsList"
                :key="index"
              >
                <div class="news-time">
                  <span class="time">{{ news.time }}</span>
                  <span class="date">{{ news.date }}</span>
                </div>
                <div class="news-content">
                  <h3 class="news-title">{{ news.title }}</h3>
                  <p class="news-summary">{{ news.summary }}</p>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
    </section>

    <!-- 底部信息 -->
    <footer class="company-footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-left">
            <p>{{ config.companyInfo.copyright }}</p>
            <p>{{ config.companyInfo.companyName }}</p>
            <p>
              <a :href="config.companyInfo.icp.url" target="_blank">
                {{ config.companyInfo.icp.text }}
              </a>
            </p>
            <p>{{ config.companyInfo.license }}</p>
            <p>{{ config.companyInfo.email }}</p>
          </div>
          <div class="footer-right">
            <div class="contact-info">
              <h4>{{ config.contactInfo.serviceTitle }}</h4>
              <p>{{ config.contactInfo.serviceTime }}</p>
              <p class="phone">{{ config.contactInfo.phone }}</p>
            </div>
            <div class="qr-code">
              <img :src="config.contactInfo.qrCode.src" :alt="config.contactInfo.qrCode.alt" />
            </div>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { companyWebConfig } from "@/config/companyWebConfig.js";

// 配置数据
const config = companyWebConfig;

// 当前激活的标签
const activeTab = ref("latest");

// 当前显示的新闻列表
const currentNewsList = computed(() => {
  return config.newsData[activeTab.value] || [];
});

// 切换标签
const switchTab = (tabKey) => {
  activeTab.value = tabKey;
};
</script>

<style scoped lang="scss">
.company-web {
  width: 100%;
  min-height: 100vh;
  background: #f8f9fa;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  // 顶部轮播图
  .hero-banner {
    width: 100%;
    height: 400px;
    overflow: hidden;

    .banner-swipe {
      height: 100%;

      .van-swipe-item {
        height: 100%;
      }

      .banner-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        user-select: none; // 防止图片被选中
        -webkit-user-drag: none; // 防止图片被拖拽
      }

      // 指示器样式优化
      :deep(.van-swipe__indicators) {
        bottom: 20px;

        .van-swipe__indicator {
          background-color: rgba(255, 255, 255, 0.5);

          &.van-swipe__indicator--active {
            background-color: #007bff;
          }
        }
      }
    }
  }

  // 主要内容区域
  .main-content {
    padding: 40px 0;
    background: #fff;

    .content-layout {
      display: flex;
      gap: 40px;
      align-items: flex-start;
    }

    // 左侧导航
    .sidebar {
      flex: 0 0 280px;

      .sidebar-header {
        padding: 30px 0;

        h3 {
          font-size: 24px;
          color: #999;
          margin: 0 0 5px 0;
          font-weight: normal;
        }

        .divider {
          width: 40px;
          height: 4px;
          background: #007bff;
          margin: 10px 0;
          border-radius: 4px;
        }

        .dot {
          background: #007bff;
          width: 4px;
          height: 4px;
          margin-left: 2px;
          border-radius: 2px;
        }

        h2 {
          font-size: 24px;
          color: #333;
          margin: 0;
          font-weight: bold;
        }
      }

      .sidebar-nav {
        .nav-item {
          padding: 10px 4px;
          cursor: pointer;
          transition: all 0.3s ease;
          color: #333;
          font-size: 16px;
          border: 2px solid transparent;
          margin-bottom: 20px;

          &:hover {
            color: #007bff;
          }

          &.active {
            color: #007bff;
            border-bottom: 2px solid #007bff;
            font-weight: 600;
            background: #fff;
            position: relative;
          }

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    // 右侧内容
    .content-area {
      flex: 1;
      background: #fff;
      border: 1px solid #e5e5e5;
      border-radius: 8px;
      padding: 30px;

      .news-list {
        .news-item {
          display: flex;
          gap: 20px;
          padding: 20px 0;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .news-time {
            flex: 0 0 80px;
            text-align: center;

            .time {
              display: block;
              font-size: 24px;
              font-weight: bold;
              color: #007bff;
              line-height: 1;
            }

            .date {
              display: block;
              font-size: 14px;
              color: #999;
              margin-top: 5px;
            }
          }

          .news-content {
            flex: 1;

            .news-title {
              font-size: 18px;
              color: #333;
              margin: 0 0 10px 0;
              line-height: 1.4;
              font-weight: 500;
              cursor: pointer;
              transition: color 0.3s ease;

              &:hover {
                color: #007bff;
              }
            }

            .news-summary {
              font-size: 14px;
              color: #666;
              line-height: 1.6;
              margin: 0;
            }
          }
        }
      }
    }
  }

  // 底部信息
  .company-footer {
    background: #f8f9fa;
    padding: 40px 0;
    border-top: 1px solid #e5e5e5;

    .footer-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: 40px;

      .footer-left {
        flex: 1;

        p {
          margin: 5px 0;
          font-size: 14px;
          color: #666;
          line-height: 1.5;

          a {
            color: #007bff;
            text-decoration: none;

            &:hover {
              text-decoration: underline;
            }
          }
        }
      }

      .footer-right {
        display: flex;
        align-items: center;
        gap: 30px;

        .contact-info {
          text-align: right;

          h4 {
            font-size: 16px;
            color: #333;
            margin: 0 0 10px 0;
          }

          p {
            margin: 5px 0;
            font-size: 14px;
            color: #666;

            &.phone {
              font-size: 18px;
              font-weight: bold;
              color: #007bff;
            }
          }
        }

        .qr-code {
          img {
            width: 80px;
            height: 80px;
            border-radius: 8px;
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .main-content {
      padding: 20px 0;

      .content-layout {
        flex-direction: column;
        gap: 20px;
      }

      .sidebar {
        flex: none;
        width: 100%;

        .sidebar-header {
          padding: 20px 15px;

          h2 {
            font-size: 20px;
          }
        }

        .sidebar-nav {
          display: flex;
          overflow-x: auto;
          white-space: nowrap;

          .nav-item {
            flex: 0 0 auto;
            padding: 12px 20px;
            border-bottom: none;
            border-right: 1px solid #f0f0f0;

            &:last-child {
              border-right: none;
            }

            &.active::after {
              display: none;
            }
          }
        }
      }

      .content-area {
        padding: 20px 15px;

        .news-list {
          .news-item {
            flex-direction: column;
            gap: 10px;
            padding: 15px 0;

            .news-time {
              flex: none;
              text-align: left;

              .time {
                font-size: 20px;
              }
            }

            .news-content {
              .news-title {
                font-size: 16px;
              }

              .news-summary {
                font-size: 13px;
              }
            }
          }
        }
      }
    }

    .company-footer {
      padding: 30px 0;

      .footer-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;

        .footer-right {
          justify-content: center;
          gap: 20px;

          .contact-info {
            text-align: center;
          }
        }
      }
    }
  }
}
</style>
