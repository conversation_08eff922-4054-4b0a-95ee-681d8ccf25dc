<template>
  <div class="company-web">
    <!-- 顶部轮播图 -->
    <section class="hero-banner">
      <van-swipe
        class="banner-swipe"
        :autoplay="5000"
        indicator-color="white"
        :touchable="true"
        :show-indicators="true"
        :loop="true"
        :duration="500"
      >
        <van-swipe-item>
          <img
            src="@/assets/images/web/RewardBanner.png"
            :alt="'奖励banner'"
            class="banner-image"
          />
        </van-swipe-item>
        <van-swipe-item>
          <img
            src="@/assets/images/web/TeamBuildingBanner.png"
            :alt="'团建banner'"
            class="banner-image"
          />
        </van-swipe-item>
      </van-swipe>
    </section>

    <!-- 主要内容区域 -->
    <section class="main-content">
      <div class="container">
        <div class="content-layout">
          <!-- 左侧导航 -->
          <aside class="sidebar">
            <div class="sidebar-header">
              <h3>LATEST</h3>
              <h3>NEWS</h3>
              <div style="display: flex; align-items: center">
                <div class="divider"></div>
                <div class="dot"></div>
              </div>
              <h2>最新动态</h2>
            </div>
            <nav class="sidebar-nav">
              <div
                class="nav-item"
                :class="{ active: activeTab === item.key }"
                v-for="item in navItems"
                :key="item.key"
                @click="switchTab(item.key)"
              >
                {{ item.label }}
              </div>
            </nav>
          </aside>

          <!-- 右侧内容 -->
          <main class="content-area">
            <div class="news-list">
              <div
                class="news-item"
                v-for="(news, index) in currentNewsList"
                :key="index"
              >
                <div class="news-time">
                  <span class="time">{{ news.time }}</span>
                  <span class="date">{{ news.date }}</span>
                </div>
                <div class="news-content">
                  <h3 class="news-title">{{ news.title }}</h3>
                  <p class="news-summary">{{ news.summary }}</p>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
    </section>

    <!-- 底部信息 -->
    <footer class="company-footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-left">
            <p>版权所有 © fushangyunfu.com</p>
            <p>广州福尚云科技研发有限公司</p>
            <p>
              <a href="https://beian.miit.gov.cn/" target="_blank"
                >粤ICP备 2024197431号-2</a
              >
            </p>
            <p>增值电信业务经营许可证：粤B2-20241521</p>
            <p><EMAIL></p>
          </div>
          <div class="footer-right">
            <div class="contact-info">
              <h4>专属服务热线</h4>
              <p>周一至周日(09:00-22:00)</p>
              <p class="phone">************</p>
            </div>
            <div class="qr-code">
              <img src="@/assets/images/mobile/qrcode2.jpg" alt="二维码" />
            </div>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";

// 轮播图数据已直接在模板中使用，无需单独定义

// 当前激活的标签
const activeTab = ref("latest");

// 导航菜单
const navItems = ref([
  { key: "news", label: "公司新闻" },
  { key: "notice", label: "公告通知" },
  { key: "recruitment", label: "招聘通道" },
]);

// 新闻数据
const newsData = ref({
  latest: [
    {
      time: "05:07",
      date: "2025",
      title: "福尚云获得优秀人才！销售总监李总参与重要会议与总经理共同商讨大事",
      summary: "公司发展迅速，人才队伍不断壮大，为企业发展注入新的活力。",
    },
    {
      time: "05:14",
      date: "2025",
      title: "福尚云平台升级完成，为企业客户提供更优质的福利管理服务",
      summary:
        "经过技术团队的不懈努力，福尚云平台完成重大升级，功能更加完善，用户体验显著提升。",
    },
    {
      time: "05:14",
      date: "2025",
      title: "福尚云与多家知名企业达成战略合作，共建员工福利生态圈",
      summary:
        "深化产业合作，整合优质资源，为企业客户提供更多元化的福利产品和服务选择。",
    },
    {
      time: "05:14",
      date: "2025",
      title: '福尚云荣获"年度最佳企业服务平台"奖项，行业地位再获认可',
      summary:
        "凭借优质的服务和创新的理念，福尚云在激烈的市场竞争中脱颖而出，获得行业权威认可。",
    },
  ],
  news: [
    {
      time: "05:10",
      date: "2025",
      title: '福尚云荣获"广州市优秀科技企业"称号',
      summary:
        "凭借在数字化服务领域的突出贡献和创新成果，福尚云获得政府部门高度认可。",
    },
    {
      time: "05:12",
      date: "2025",
      title: "福尚云福利管理系统3.0正式发布",
      summary:
        "全新版本系统功能更加强大，支持更多福利类型，操作更加便捷，为企业客户带来全新体验。",
    },
    {
      time: "05:13",
      date: "2025",
      title: "福尚云参加第十届中国企业服务博览会",
      summary: "公司携最新产品和解决方案亮相展会，吸引众多企业客户关注和咨询。",
    },
  ],
  notice: [
    {
      time: "05:08",
      date: "2025",
      title: "关于春节放假安排的通知",
      summary:
        "根据国家法定节假日安排，福尚云将于1月28日至2月5日放假，期间客服热线正常服务。",
    },
    {
      time: "05:09",
      date: "2025",
      title: "福尚云平台系统维护升级公告",
      summary:
        "为提升系统性能和用户体验，将于本周末进行系统维护升级，预计维护时间2小时。",
    },
    {
      time: "05:11",
      date: "2025",
      title: "关于调整部分服务价格的通知",
      summary:
        "为了更好地为客户提供优质服务，公司将对部分增值服务价格进行调整，详情请咨询客服。",
    },
  ],
  recruitment: [
    {
      time: "05:15",
      date: "2025",
      title: "招聘前端开发工程师（Vue.js方向）",
      summary:
        "岗位要求：熟练掌握Vue.js、TypeScript等技术栈，有3年以上前端开发经验，薪资面议。",
    },
    {
      time: "05:16",
      date: "2025",
      title: "招聘产品经理（企业服务方向）",
      summary:
        "岗位要求：具备产品规划和设计能力，有B端产品经验优先，了解企业服务市场。",
    },
    {
      time: "05:17",
      date: "2025",
      title: "招聘销售经理（企业客户方向）",
      summary:
        "岗位要求：有企业客户销售经验，沟通能力强，具备一定的市场开拓能力。",
    },
  ],
});

// 当前显示的新闻列表
const currentNewsList = computed(() => {
  return newsData.value[activeTab.value] || [];
});

// 切换标签
const switchTab = (tabKey) => {
  activeTab.value = tabKey;
};
</script>

<style scoped lang="scss">
.company-web {
  width: 100%;
  min-height: 100vh;
  background: #f8f9fa;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  // 顶部轮播图
  .hero-banner {
    width: 100%;
    height: 400px;
    overflow: hidden;

    .banner-swipe {
      height: 100%;

      .van-swipe-item {
        height: 100%;
      }

      .banner-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        user-select: none; // 防止图片被选中
        -webkit-user-drag: none; // 防止图片被拖拽
      }

      // 指示器样式优化
      :deep(.van-swipe__indicators) {
        bottom: 20px;

        .van-swipe__indicator {
          background-color: rgba(255, 255, 255, 0.5);

          &.van-swipe__indicator--active {
            background-color: #007bff;
          }
        }
      }
    }
  }

  // 主要内容区域
  .main-content {
    padding: 40px 0;
    background: #fff;

    .content-layout {
      display: flex;
      gap: 40px;
      align-items: flex-start;
    }

    // 左侧导航
    .sidebar {
      flex: 0 0 280px;

      .sidebar-header {
        padding: 30px 0;

        h3 {
          font-size: 24px;
          color: #999;
          margin: 0 0 5px 0;
          font-weight: normal;
        }

        .divider {
          width: 40px;
          height: 4px;
          background: #007bff;
          margin: 10px 0;
          border-radius: 4px;
        }

        .dot {
          background: #007bff;
          width: 4px;
          height: 4px;
          margin-left: 2px;
          border-radius: 2px;
        }

        h2 {
          font-size: 24px;
          color: #333;
          margin: 0;
          font-weight: bold;
        }
      }

      .sidebar-nav {
        .nav-item {
          padding: 10px 4px;
          cursor: pointer;
          transition: all 0.3s ease;
          color: #333;
          font-size: 16px;
          border: 2px solid transparent;
          margin-bottom: 20px;

          &:hover {
          }

          &.active {
            color: #007bff;
            border-bottom: 2px solid #007bff;
            font-weight: 600;
            background: #fff;
            position: relative;
          }

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    // 右侧内容
    .content-area {
      flex: 1;
      background: #fff;
      border: 1px solid #e5e5e5;
      border-radius: 8px;
      padding: 30px;

      .news-list {
        .news-item {
          display: flex;
          gap: 20px;
          padding: 20px 0;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .news-time {
            flex: 0 0 80px;
            text-align: center;

            .time {
              display: block;
              font-size: 24px;
              font-weight: bold;
              color: #007bff;
              line-height: 1;
            }

            .date {
              display: block;
              font-size: 14px;
              color: #999;
              margin-top: 5px;
            }
          }

          .news-content {
            flex: 1;

            .news-title {
              font-size: 18px;
              color: #333;
              margin: 0 0 10px 0;
              line-height: 1.4;
              font-weight: 500;
              cursor: pointer;
              transition: color 0.3s ease;

              &:hover {
                color: #007bff;
              }
            }

            .news-summary {
              font-size: 14px;
              color: #666;
              line-height: 1.6;
              margin: 0;
            }
          }
        }
      }
    }
  }

  // 底部信息
  .company-footer {
    background: #f8f9fa;
    padding: 40px 0;
    border-top: 1px solid #e5e5e5;

    .footer-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: 40px;

      .footer-left {
        flex: 1;

        p {
          margin: 5px 0;
          font-size: 14px;
          color: #666;
          line-height: 1.5;

          a {
            color: #007bff;
            text-decoration: none;

            &:hover {
              text-decoration: underline;
            }
          }
        }
      }

      .footer-right {
        display: flex;
        align-items: center;
        gap: 30px;

        .contact-info {
          text-align: right;

          h4 {
            font-size: 16px;
            color: #333;
            margin: 0 0 10px 0;
          }

          p {
            margin: 5px 0;
            font-size: 14px;
            color: #666;

            &.phone {
              font-size: 18px;
              font-weight: bold;
              color: #007bff;
            }
          }
        }

        .qr-code {
          img {
            width: 80px;
            height: 80px;
            border-radius: 8px;
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .main-content {
      padding: 20px 0;

      .content-layout {
        flex-direction: column;
        gap: 20px;
      }

      .sidebar {
        flex: none;
        width: 100%;

        .sidebar-header {
          padding: 20px 15px;

          h2 {
            font-size: 20px;
          }
        }

        .sidebar-nav {
          display: flex;
          overflow-x: auto;
          white-space: nowrap;

          .nav-item {
            flex: 0 0 auto;
            padding: 12px 20px;
            border-bottom: none;
            border-right: 1px solid #f0f0f0;

            &:last-child {
              border-right: none;
            }

            &.active::after {
              display: none;
            }
          }
        }
      }

      .content-area {
        padding: 20px 15px;

        .news-list {
          .news-item {
            flex-direction: column;
            gap: 10px;
            padding: 15px 0;

            .news-time {
              flex: none;
              text-align: left;

              .time {
                font-size: 20px;
              }
            }

            .news-content {
              .news-title {
                font-size: 16px;
              }

              .news-summary {
                font-size: 13px;
              }
            }
          }
        }
      }
    }

    .company-footer {
      padding: 30px 0;

      .footer-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;

        .footer-right {
          justify-content: center;
          gap: 20px;

          .contact-info {
            text-align: center;
          }
        }
      }
    }
  }
}
</style>
