<template>
  <div class="home" ref="home">
    <div class="web" v-if="isPC">
      <div class="header">
        <div class="headImg">
          <img src="@/assets/images/mobile/fsylogo.png" alt="" srcset="" />
        </div>
        <div style="display: flex;align-items: center;">
          <div class="nav-group">
            <div :class="currentPage === 0 ? 'nav-item active' : 'nav-item'" @click="switchPage(0)">首页</div>
            <div :class="currentPage === 1 ? 'nav-item active' : 'nav-item'"  @click="switchPage(1)">2025招聘通道</div>
            <div :class="currentPage === 2 ? 'nav-item active' : 'nav-item'"  @click="switchPage(2)">企业风采</div>
          </div>
          <div class="investment" @click="toInvestment">投资者可咨询</div>
          <div class="serviceInfo">
            <div class="info">客服时间(09:00-22:00)</div>
            <div class="tel">************</div>
          </div>
        </div>
      </div>
      <HomeContent v-if="currentPage === 0" />
      <div v-else-if="currentPage === 1">
        <!-- 2025招聘通道页面内容 -->
      </div>
      <Company v-else-if="currentPage === 2" />
    </div>
    <div class="mobile" v-else>
      <div class="header">
        <img src="@/assets/images/mobile/fsylogo.png" alt="" srcset="" />
        <div class="headerLeft" @click="changeFL" style="display: flex;align-items: center;">
          <div class="investment" @click.stop="toInvestment">投资者可咨询</div>
          <van-icon name="cross" v-if="showFL" size="24px"/>
          <van-icon name="wap-nav"  size="24px" v-else />
        </div>
      </div>
      <HomeContent v-if="currentPage === 0" />
      <div v-else-if="currentPage === 1">
        <!-- 2025招聘通道页面内容 -->
      </div>
      <Company v-else-if="currentPage === 2" />
      <div class="menu" v-if="showFL">
         <div class="menuItem" v-for="(item,j) in menuList" :key="j" @click="changeTop(item.value)">{{ item.lable }}</div>
      </div>
    </div>
    <div class="gototop">
      <van-button
        class="back-top"
        round
        icon="back-top"
        color="#4677ff"
        @click="goTop"
      />
    </div>
  </div>
  <!-- 浮窗 -->
  <div v-if="iframeShow" class="floating-window" :class="{'webFloating':isPC}">
    <iframe :src="iframeUrl" frameborder="0" class="iframe"></iframe>
    <img src="@/assets/images/close.png" class="close-btn1" @click="()=>iframeShow = false" />
  </div>
  <div v-if="showInvestment" class="investmentPopup" :class="{'webPopup':isPC}">
    <div class="investmentPopup-top">
      <img src="@/assets/images/zst.png" class="investmentPopup-img" />
    </div>
    <div class="investmentPopup-content">
      <div class="floatingTitleBox">
        <div class="floatingTitle">请确认您已符合以下任一条件</div>
        <div class="floatingInfo">1.拥有国资背景；</div>
        <div class="floatingInfo">2.完成过IPO上市；</div>
        <div class="floatingInfo" style="margin-bottom: 15px;">3.私募基金机构。</div>
        <div class="floatingInfo">若您符合以上任一项，请填表留下联系方式，我们将尽快与您联系~</div>
      </div>
      <div style="width: 100%;display: flex;justify-content: center;align-items: center;">
        <div  class="investmentPopup-goBtn" @click="goInvestment">填写联系信息</div>
      </div>
    </div>
    <img src="@/assets/images/close.png" class="close-btn" @click="()=>showInvestment=false" />
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { indexStore } from "@/store/index.js";
import HomeContent from "../home/<USER>";
import Company from "../company/company.vue"; // 引入Company组件

// 响应式数据
const currentPage = ref(0);
const showFL = ref(false);
const menuList = ref([
  { lable: '关于我们', value: '1' },
  { lable: '产品介绍', value: '2' },
  { lable: '电影通兑', value: '3' },
  { lable: '生日福利', value: '4' },
  { lable: '年节礼包', value: '5' },
  { lable: '云福优选', value: '6' },
  { lable: '合作伙伴', value: '7' },
  { lable: '平台优势', value: '8' },
  { lable: '联系我们', value: '9' },
]);
const showInvestment = ref(false);
const iframeUrl = ref("https://voucher.fushangyunfu.com/");
const iframeShow = ref(false);
const home = ref(null);

// 计算属性
const isPC = computed(() => {
  return indexStore().isPC;
});



// 方法
const switchPage = (i) => {
  currentPage.value = i;
};

const goTop = () => {
  home.value.scrollTop = 0;
};

const changeFL = () => {
  showFL.value = !showFL.value;
};

const changeTop = (val) => {
  // 保持原有逻辑
  showFL.value = false;
};

const toInvestment = () => {
  showInvestment.value = true;
};

const goInvestment = () => {
  showInvestment.value = false;
  iframeShow.value = true;
};
</script>

<style scoped lang="scss">
 .home {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    overflow: auto;
    background: #fff;
    .mobile {
      width: 100%;
      height: 100%;
      padding-top: 50px;
      position: relative;
      .menu{
        width: 100%;
        height: calc(100% - 50px);
        position: fixed;
        top: 50px;
        left: 0px;
        background-color: rgba($color: #000000, $alpha: 0.7);
        padding: 15px;
        .menuItem{
          width: 100%;
          height: 48px;
          line-height: 48px;
          text-align: center;
          color: #fff;
          border-bottom: 1px solid #555;
  
        }
      }
      .header {
        width: 100%;
        height: 50px;
        padding: 5px 10px 0px;
        background: #fff;
        
        img {
          width: 120px;
          height: 32px;
        }
        position: fixed;
        top: 0;
        z-index: 9;
        display: flex;
        justify-content: space-between;
        align-items: center;
        
      }
      .my-swipe {
        width: 100%;
        img {
          width: 100%;
        }
      }
      .about {
        width: 100%;
        background-color: #f5f5f5;
        padding-bottom: 20px;
        line-height: 20px;
        img {
          width: 100%;
        }
        .introduct {
          font-size: 13px;
          color: #333;
          text-indent: 2em;
          padding: 0px 15px 0 20px;
          margin-bottom: 6px;
        }
      }
      .product {
        width: 100%;
        img {
          width: 100%;
        }
      }
      .companyInfo {
        width: 100%;
        background: #212932;
        color: #fff;
        position: relative;
        top: -5px;
        padding: 20px 10px;
        .service {
          text-align: center;
          width: 100%;
          font-size: 12px;
          .tel {
            font-size: 24px;
          }
        }
        .qrCode {
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          width: 100%;
          height: 170px;
          font-size: 12px;
          img {
            width: 130px;
            height: 130px;
          }
          .info {
            width: 100%;
            height: 32px;
            text-align: center;
            line-height: 32px;
            color: #eee;
          }
        }
        .forRecord {
          font-size: 12px;
          color: #eee;
          text-align: center;
        }
      }
    }
    .web {
      width: 100%;
      height: 100%;
      padding-top: 80px;
      .header {
        width: 100%;
        height: 80px;
        padding: 0 50px;
        background: #fff;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        position: fixed;
        top: 0;
        left: 0;
        z-index: 2099;
        .headImg {
          width: 512px;
          height: 108px;
          display: flex;
          justify-content: center;
          align-items: center;
          img {
            width: 256px;
            height: 54px;
          }
        }
        .serviceInfo {
          width: 200px;
          height: 80px;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          font-size: 12px;
          line-height: 24px;
          color: #666;
          text-align: center;
          .tel {
            font-size: 18px;
            color: #333;
          }
        }
        .headNav {
          font-size: 18px;
          color: #333;
          height: 32px;
          line-height: 32px;
          display: flex;
          align-items: center;
          flex-shrink: 0;
          .hnItem {
            padding: 0 5px;
            margin-left: 50px;
          }
          .active {
            color: #4182ff;
            border-bottom: 2px solid #4182ff;
          }
        }
      }
      .my-swipe {
        width: 100%;
        .van-swipe-item {
          width: 100%;
          .swipeImg {
            width: 100%;
          }
        }
      }
      .title {
        width: 100%;
        text-align: center;
        color: #333;
        font-size: 30px;
      }
      .eglx {
        line-height: 28px;
        font-size: 16px;
        color: #777;
        text-align: center;
        margin-bottom: 20px;
      }
      .about {
        width: 100%;
        background-color: #f5f5f5;
        padding: 50px;
  
        .detali {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          .aboutImgWeb {
            width: 340px;
            height: 250px;
            margin-right: 30px;
          }
          .info {
            width: 450px;
            text-indent: 2em;
            font-size: 14px;
            color: #333;
            line-height: 24px;
          }
        }
      }
      .fuwu {
        width: 100%;
        padding: 50px;
        .fwcontent {
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          .fwItem {
            width: 340px;
            height: 240px;
            margin-right: 30px;
            position: relative;
            overflow: hidden;
            img {
              width: 100%;
              height: 100%;
              transition: 2s;
            }
            img:hover {
              transform: scale(1.2);
            }
            .fwInfo {
              width: 100%;
              height: 100%;
              position: absolute;
              top: 0;
              left: 0;
              display: flex;
              justify-content: center;
              align-items: center;
              flex-direction: column;
              color: #fff;
              .main {
                width: 100%;
                font-size: 24px;
                text-align: center;
              }
              .second {
                width: 160px;
                font-size: 14px;
                text-align: center;
              }
            }
          }
          .fwItem:hover img {
            transform: scale(1.2);
          }
        }
      }
      .youxuan {
        width: 1500px;
        padding: 50px;
        margin: 0 auto;
        .fwcontent {
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-wrap: wrap;
          .fwItem {
            width: 250px;
            height: 230px;
            margin: 0px 30px 30px 0;
            position: relative;
            overflow: hidden;
            img {
              width: 100%;
              height: 100%;
              transition: 2s;
            }
            img:hover {
              transform: scale(1.2);
            }
            .fwInfo {
              width: 100%;
              height: 100%;
              position: absolute;
              top: 0;
              left: 0;
              display: flex;
              justify-content: center;
              align-items: center;
              flex-direction: column;
              color: #fff;
              .main {
                width: 100%;
                font-size: 24px;
                text-align: center;
              }
              .second {
                width: 160px;
                font-size: 14px;
                text-align: center;
              }
            }
          }
          .fwItem:hover img {
            transform: scale(1.2);
          }
        }
      }
      .shengri {
        width: 100%;
        padding: 50px;
        background: #f5f5f5;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        img {
          width: 1100px;
          height: 330px;
        }
      }
      .moviebg {
        width: 100%;
        img {
          width: 100%;
        }
      }
      .hezuo {
        width: 100%;
        padding: 50px;
        background: #fff;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        img {
          width: 1100px;
        }
      }
      .youshi {
        width: 100%;
        img {
          width: 100%;
        }
      }
      .footer {
        width: 100%;
        height: 120px;
        padding: 0px 50px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #333;
        font-size: 14px;
        box-sizing: border-box;
        margin-top: 50px;
        padding-bottom: 10px;
  
        .left {
          line-height: 24px;
        }
        .right {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .kefu {
            line-height: 24px;
            margin-right: 50px;
            .info {
              color: #666;
            }
            .tel {
              font-size: 24px;
            }
          }
          .qrCode {
            width: 90px;
            height: 90px;
            img {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }
    .gototop {
      width: 40px;
      height: 40px;
      position: fixed;
      bottom: 80px;
      right: 10px;
      .back-top {
        width: 40px;
        height: 40px;
        font-size: 20px;
        font-weight: bolder;
        box-shadow: inset 0 0 5px #4182ff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: auto;
      }
    }
  }

  a{
  text-decoration: underline;
  color: #4182ff;
}
.investment{
  font-size: 24px;
  background: linear-gradient(to right, #65A5F8, #456BF5);
  -webkit-background-clip: text;
  color: transparent;
  margin-right: 10px;
  cursor: pointer;
}

.floating-window {
  position: fixed;
  background-color: #fff;
  top: 10%;
  left: 10%;
  width: 80%;
  height: 80%;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  border-radius: 8px;
  overflow: hidden;
  .close-btn1 {
    position: absolute;
    top: 0px;
    right: 0px;
    border: none;
    width: 30.5px;
    height: 30.5px;
    cursor: pointer;
    z-index: 99999;
  }
}

.webFloating{
  position: fixed;
  background-color: #fff;
  top: 10%;
  left: 25%;
  width: 50%;
  height: 80%;
}


.iframe {
  width: 100%;
  height: 100%;
}

.close-btn {
  position: absolute;
  top: 10px;
  // right: 46%;
  right: 10px;
  border: none;
  width: 30.5px;
  height: 30.5px;
  cursor: pointer;
}


.floatingBox{
  padding:16px 5%  0px 12%;
  background: linear-gradient(to bottom, #E8F1FF, #D1E2FF);

  

  .floatingImg{
    width: 181px;
    height: 135px;
    align-self: flex-end;
    position: absolute;
    right: 12%;
    bottom:0;
  }
}

.floatingTitleBox{
  margin-bottom: 15px;
  margin-top: 2%;
  .floatingTitle{
    font-size: 24px;
    font-weight: bold;
    color: #3891E4;
    margin-bottom: 12px;
  }
  .floatingInfo{
    font-size: 18px;
    color:#333;
  }
}

.investmentPopup{
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
}
.webPopup{
  width: 50%;
}

.investmentPopup-top{
  width: 100%;
  height: 150px;
  background: linear-gradient(to bottom, #E8F1FF, #D1E2FF);
  position: relative;


  .investmentPopup-img{
    width: 181px;
    height: 135px;
    position: absolute;
    right: 0%;
    bottom: 0;
  }
}

.investmentPopup-content{
  background: #fff;
  padding:16px 10%  30px 8%;
}

.investmentPopup-goBtn{
  text-align: center;
  font-size: 20px;
  color: #fff;
  background: #0067fc;
  padding:10px 20px;
  border-radius: 8px;
  margin-top: 5%;
  cursor: pointer;
}


  .nav-group{
    display: flex;
    align-items: center;
    margin-right: 40px;

    .nav-item {
      padding: 0 10px;
      font-size: 14px;
      color: #333;
      margin-left: 10px;
      cursor: pointer;

      &.active {
        color: #4182ff;
      }
    }
  }
  
</style>
