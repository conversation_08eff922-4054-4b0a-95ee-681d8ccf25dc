<template>
    <div class="home" ref="home">
      <div class="web" v-if="isPC">
        <div class="header">
          <div class="headImg">
            <img src="@/assets/images/mobile/fsylogo.png" alt="" srcset="" />
          </div>
          <div style="display: flex;align-items: center;">
            <div class="investment" @click="toInvestment">投资者可咨询</div>
            <div class="serviceInfo">
              <div class="info">客服时间(09:00-22:00)</div>
              <div class="tel">************</div>
            </div>
          </div>
        </div>
        <van-swipe class="my-swipe" :autoplay="3000" indicator-color="white">
          <van-swipe-item v-for="(item, idx) in swipeListWeb" :key="idx">
            <img :src="item.imgUrl" alt="" srcset="" class="swipeImg" />
          </van-swipe-item>
        </van-swipe>
        <div class="about">
          <div class="title">关于我们</div>
          <div class="eglx">ABOUT US</div>
          <div class="detali">
            <img
              src="@/assets/images/web/about.png"
              alt=""
              srcset=""
              class="aboutImgWeb"
            />
            <div class="info">
              <div class="introduct">
                福尚云位于广州，福尚云专业向全国企业客户提供各类工会福利产品、礼赠品、商务用品、办公用品等。包括节日慰问品、电影票、生日蛋糕、优选购物、劳保用品、办公用品、礼赠品、企业工装定制等综合性服务。福尚云顺应市场发展趋势，整合各类资源，辅以技术创新，让客户可选多元化、高品质的产品及服务。
              </div>
              <div class="introduct">
                福尚云开发的“福尚云系统”可以为企业量身定制专属的福利管理综合平台，为企业员工提供无接触式福利发放。员工在福利平台中可以自主选择适合自己的福利，企业只需要制定福利投入预算即可。有效优化企业福利管理效率，降低企业管理者在福利发放中人力、物力的成本，提升员工归属感和满意度，满足员工自主的选择权力。
              </div>
            </div>
          </div>
        </div>
        <div class="fuwu">
          <div class="title">定制服务</div>
          <div class="eglx">CUSTOMIZED SERVICE</div>
          <div class="fwcontent">
            <div class="fwItem" v-for="(item, j) in dingZhiImg" :key="j">
              <img :src="item.imgUrl" alt="" />
              <div class="fwInfo">
                <div class="main">{{ item.title }}</div>
                <div class="second">{{ item.info }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="moviebg">
          <img src="@/assets/images/web/yingyuantongdui.jpg" alt="" />
        </div>
        <div class="shengri">
          <div class="title">生日福利</div>
          <div class="eglx">BIRTHDAY CAKE</div>
          <img src="@/assets/images/web/shengri2.jpg" alt="" />
        </div>
        <div class="hezuo">
          <div class="title">合作伙伴</div>
          <div class="eglx">COOPERATIVE PARTNER</div>
          <img src="@/assets/images/web/hezuo.jpg" alt="" />
        </div>
        <div class="youxuan">
          <div class="title">云福优选</div>
          <div class="eglx">YUNFU MALL</div>
          <div class="fwcontent">
            <div class="fwItem" v-for="(item, j) in youxuan" :key="j">
              <img :src="item.imgUrl" alt="" />
              <div class="fwInfo">
                <div class="main">{{ item.title }}</div>
                <div class="second">{{ item.info }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="youshi">
          <img src="@/assets/images/web/wmdyx.png" alt="" />
        </div>
        <div class="footer">
          <div class="left">
            <div class="forthe">版权所有 © fushangyunfu.com</div>
            <div class="forthe">广州福尚云科技研发有限公司</div>
            <div class="forthe">
              <a href="https://beian.miit.gov.cn/" target="_blank" >粤ICP备 </a>  
              <a href="https://beian.miit.gov.cn/" target="_blank" > 2024197431号-2</a>
            </div>
            <div class="forthe">增值电信业务经营许可证：粤B2-20241521</div>
            <div><EMAIL></div>
          </div>
          <div class="right">
            <div class="kefu">
              <div class="biaoti">专属服务热线</div>
              <div class="info">周一至周日(09:00-22:00)</div>
              <div class="tel">************</div>
            </div>
            <div class="qrCode">
              <img src="@/assets/images/mobile/qrcode2.jpg" alt="" />
            </div>
          </div>
        </div>
      </div>
      <div class="mobile" v-else>
        <div class="header">
          <img src="@/assets/images/mobile/fsylogo.png" alt="" srcset="" />
          <div class="headerLeft" @click="changeFL" style="display: flex;align-items: center;">
            <div class="investment" @click.stop="toInvestment">投资者可咨询</div>
            <van-icon name="cross" v-if="showFL" size="24px"/>
            <van-icon name="wap-nav"  size="24px" v-else />
          </div>
        </div>
        <van-swipe class="my-swipe" :autoplay="3000" indicator-color="white">
          <van-swipe-item v-for="(item, idx) in swipeList" :key="idx">
            <img :src="item.imgUrl" alt="" srcset="" />
          </van-swipe-item>
        </van-swipe>
        <div class="about" >
          <img src="@/assets/images/mobile/gywm.png" alt="" />
          <div class="introduct">
            福尚云位于广州，福尚云专业向全国企业客户提供各类工会福利产品、礼赠品、商务用品、办公用品等。包括节日慰问品、电影票、生日蛋糕、优选购物、劳保用品、办公用品、礼赠品、企业工装定制等综合性服务。福尚云顺应市场发展趋势，整合各类资源，辅以技术创新，让客户可选多元化、高品质的产品及服务。
          </div>
          <div class="introduct">
            福尚云开发的“福尚云系统”可以为企业量身定制专属的福利管理综合平台，为企业员工提供无接触式福利发放。员工在福利平台中可以自主选择适合自己的福利，企业只需要制定福利投入预算即可。有效优化企业福利管理效率，降低企业管理者在福利发放中人力、物力的成本，提升员工归属感和满意度，满足员工自主的选择权力。
          </div>
        </div>
  
        <div class="product" >
          <img src="@/assets/images/mobile/cpjs.png" alt="" />
        </div>
        <div class="product" >
          <img src="@/assets/images/mobile/dianying.png" alt="" />
        </div>
        <div class="product" i>
          <img src="@/assets/images/mobile/srfl2.png" alt="" />
        </div>
        <div class="product" >
          <img src="@/assets/images/mobile/njlb.png" alt="" />
        </div>
        <div class="product" >
          <img src="@/assets/images/mobile/xmyx.png" alt="" />
        </div>
        <div class="product" >
          <img src="@/assets/images/mobile/hzhb.png" alt="" />
        </div>
        <div class="product" >
          <img src="@/assets/images/mobile/wmdys.png" alt="" />
        </div>
        <div class="companyInfo"  >
          <div class="service">
            <div class="info">客服时间(09:00-22:00)</div>
            <a class="tel" href="tel:4007781600">************</a>
          </div>
          <div class="qrCode">
            <img src="@/assets/images/mobile/qrcode2.jpg" alt="" />
            <div class="info">长按识别或截图保存关注 ‘‘福尚云’’ 公众号</div>
          </div>
          <div class="forRecord">
            <div class="forthe">版权所有 © fushangyunfu.com</div>
            <div class="forthe">广州福尚云科技研发有限公司</div>
            <div class="forthe">
              <a href="https://beian.miit.gov.cn/" target="_blank" >粤ICP备 </a> 
              <a href="https://beian.miit.gov.cn/" target="_blank" > 2024197431号-2</a> 
            </div>
            <div class="forthe">增值电信业务经营许可证：粤B2-20241521</div>
            <div class="forthe"><EMAIL></div>
          </div>
        </div>
        <div class="menu" v-if="showFL">
           <div class="menuItem" v-for="(item,j) in menuList" :key="j" @click="changeTop(item.value)">{{ item.lable }}</div>
        </div>
      </div>
      <div class="gototop">
        <van-button
          class="back-top"
          round
          icon="back-top"
          color="#4677ff"
          @click="goTop"
        />
      </div>
    </div>
    <!-- 浮窗 -->
    <div v-if="iframeShow" class="floating-window" :class="{'webFloating':isPC}">
      <iframe :src="iframeUrl" frameborder="0" class="iframe"></iframe>
      <img src="@/assets/images/close.png" class="close-btn1" @click="()=>iframeShow = false" />
    </div>
  
    <div v-if="showInvestment" class="investmentPopup" :class="{'webPopup':isPC}">
      <div class="investmentPopup-top">
        <img src="@/assets/images/zst.png" class="investmentPopup-img" />
      </div>
      <div class="investmentPopup-content">
        <div class="floatingTitleBox">
          <div class="floatingTitle">请确认您已符合以下任一条件</div>
          <div class="floatingInfo">1.拥有国资背景；</div>
          <div class="floatingInfo">2.完成过IPO上市；</div>
          <div class="floatingInfo" style="margin-bottom: 15px;">3.私募基金机构。</div>
          <div class="floatingInfo">若您符合以上任一项，请填表留下联系方式，我们将尽快与您联系~</div>
        </div>
        <div style="width: 100%;display: flex;justify-content: center;align-items: center;">
          <div  class="investmentPopup-goBtn" @click="goInvestment">填写联系信息</div>
        </div>
      </div>
      <img src="@/assets/images/close.png" class="close-btn" @click="()=>showInvestment=false" />
    </div>
  </template>
  
  <script>
  import { ref } from "vue";
  import { indexStore } from "@/store/index.js";
  import img1 from "@/assets/images/mobile/phonebanner.png";
  import img2 from "@/assets/images/web/banner.png";
  import img3 from "@/assets/images/web/dz1.jpg";
  import img4 from "@/assets/images/web/dz2.jpg";
  import img5 from "@/assets/images/web/dz3.jpg";
  
  import img11 from "@/assets/images/web/yx1.jpg";
  import img12 from "@/assets/images/web/yx2.jpg";
  import img13 from "@/assets/images/web/yx3.jpg";
  import img14 from "@/assets/images/web/yx4.jpg";
  import img15 from "@/assets/images/web/yx5.jpg";
  import img16 from "@/assets/images/web/yx6.jpg";
  import img17 from "@/assets/images/web/yx7.jpg";
  import img18 from "@/assets/images/web/yx8.jpg";
  import img19 from "@/assets/images/web/yx9.jpg";
  import img20 from "@/assets/images/web/yx10.jpg";
  export default {
    name: "home",
    data() {
      return {
        title: "福尚云首页",
        swipeList: [{ imgUrl: ref(img1), url: "" }],
        active: 0,
        navList: [{ lable: "首页", value: 0 }],
        swipeListWeb: [{ imgUrl: ref(img2), url: "" }],
        dingZhiImg: [
          {
            imgUrl: ref(img3),
            title: "平台定制",
            info: "企业专属SAAS平台定制个性化企业定制服务",
          },
          {
            imgUrl: ref(img4),
            title: "产品定制",
            info: "根据企业产品偏好定制专属福利产品方案",
          },
          {
            imgUrl: ref(img5),
            title: "设计定制",
            info: "卡券、卡套、生日贺卡、公众号等专属定制",
          },
        ],
        youxuan: [
          {
            imgUrl: ref(img11),
            title: "食品",
          },
          {
            imgUrl: ref(img12),
            title: "洗护",
          },
          {
            imgUrl: ref(img13),
            title: "居家",
          },
          {
            imgUrl: ref(img14),
            title: "家电",
          },
          {
            imgUrl: ref(img15),
            title: "餐厨",
          },
          {
            imgUrl: ref(img16),
            title: "母婴",
          },
          {
            imgUrl: ref(img17),
            title: "图书",
          },
          {
            imgUrl: ref(img18),
            title: "办公",
          },
          {
            imgUrl: ref(img19),
            title: "生鲜",
          },
          {
            imgUrl: ref(img20),
            title: "票务",
          },
        ],
        showFL:false ,
        menuList :[
          {lable:'关于我们',value:'1'},
          {lable:'产品介绍',value:'2'},
          {lable:'电影通兑',value:'3'},
          {lable:'生日福利',value:'4'},
          {lable:'年节礼包',value:'5'},
          {lable:'云福优选',value:'6'},
          {lable:'合作伙伴',value:'7'},
          {lable:'平台优势',value:'8'},
          {lable:'联系我们',value:'9'},
        ],
        showInvestment: false,
        iframeUrl: "https://voucher.fushangyunfu.com/",
        iframeShow:false
      };
    },
    mounted() {},
    computed: {
      isPC() {
        return indexStore().isPC;
      },
    },
    methods: {
      // 切换导航栏
      changeNav(i) {
        this.active = i;
      },
      //返回顶部
      goTop() {
        const home = ref(null);
        this.$refs.home.scrollTop = 0;
      },
      //切换显示分类
      changeFL(){
        this.showFL = !this.showFL
      },
      // 点击切换高度
      changeTop(val){
        if(val==='1'){
          this.$refs.home.scrollTop = 100;   
        }
        if(val==='2'){
          this.$refs.home.scrollTop = 400;
        }
        if(val==='3'){
          this.$refs.home.scrollTop = 1300;
        }
        if(val==='4'){
          this.$refs.home.scrollTop = 1600;
        }
        if(val==='5'){
          this.$refs.home.scrollTop = 1900;
        }
        if(val==='6'){
          this.$refs.home.scrollTop = 2300;
        }
        if(val==='7'){
          this.$refs.home.scrollTop = 2900;
        }
        if(val==='8'){
          this.$refs.home.scrollTop = 3600;
        } 
        if(val==='9'){
          this.$refs.home.scrollTop = 4000;;
        }
        this.showFL = false
      },
      // 打开浮窗
      toInvestment() {
        this.showInvestment = true;
      },
      goInvestment(){
        this.showInvestment = false
        this.iframeShow = true
      }
  
    },
  };
  </script>
  
  <style scoped lang="scss">
  .home {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    overflow: auto;
    background: #fff;
    .mobile {
      width: 100%;
      height: 100%;
      padding-top: 50px;
      position: relative;
      .menu{
        width: 100%;
        height: calc(100% - 50px);
        position: fixed;
        top: 50px;
        left: 0px;
        background-color: rgba($color: #000000, $alpha: 0.7);
        padding: 15px;
        .menuItem{
          width: 100%;
          height: 48px;
          line-height: 48px;
          text-align: center;
          color: #fff;
          border-bottom: 1px solid #555;
  
        }
      }
      .header {
        width: 100%;
        height: 50px;
        padding: 5px 10px 0px;
        background: #fff;
        
        img {
          width: 120px;
          height: 32px;
        }
        position: fixed;
        top: 0;
        z-index: 9;
        display: flex;
        justify-content: space-between;
        align-items: center;
        
      }
      .my-swipe {
        width: 100%;
        img {
          width: 100%;
        }
      }
      .about {
        width: 100%;
        background-color: #f5f5f5;
        padding-bottom: 20px;
        line-height: 20px;
        img {
          width: 100%;
        }
        .introduct {
          font-size: 13px;
          color: #333;
          text-indent: 2em;
          padding: 0px 15px 0 20px;
          margin-bottom: 6px;
        }
      }
      .product {
        width: 100%;
        img {
          width: 100%;
        }
      }
      .companyInfo {
        width: 100%;
        background: #212932;
        color: #fff;
        position: relative;
        top: -5px;
        padding: 20px 10px;
        .service {
          text-align: center;
          width: 100%;
          font-size: 12px;
          .tel {
            font-size: 24px;
          }
        }
        .qrCode {
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          width: 100%;
          height: 170px;
          font-size: 12px;
          img {
            width: 130px;
            height: 130px;
          }
          .info {
            width: 100%;
            height: 32px;
            text-align: center;
            line-height: 32px;
            color: #eee;
          }
        }
        .forRecord {
          font-size: 12px;
          color: #eee;
          text-align: center;
        }
      }
    }
    .web {
      width: 100%;
      height: 100%;
      padding-top: 80px;
      .header {
        width: 100%;
        height: 80px;
        padding: 0 50px;
        background: #fff;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        position: fixed;
        top: 0;
        left: 0;
        z-index: 2099;
        .headImg {
          width: 512px;
          height: 108px;
          display: flex;
          justify-content: center;
          align-items: center;
          img {
            width: 256px;
            height: 54px;
          }
        }
        .serviceInfo {
          width: 200px;
          height: 80px;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          font-size: 12px;
          line-height: 24px;
          color: #666;
          text-align: center;
          .tel {
            font-size: 18px;
            color: #333;
          }
        }
        .headNav {
          font-size: 18px;
          color: #333;
          height: 32px;
          line-height: 32px;
          display: flex;
          align-items: center;
          flex-shrink: 0;
          .hnItem {
            padding: 0 5px;
            margin-left: 50px;
          }
          .active {
            color: #4182ff;
            border-bottom: 2px solid #4182ff;
          }
        }
      }
      .my-swipe {
        width: 100%;
        .van-swipe-item {
          width: 100%;
          .swipeImg {
            width: 100%;
          }
        }
      }
      .title {
        width: 100%;
        text-align: center;
        color: #333;
        font-size: 30px;
      }
      .eglx {
        line-height: 28px;
        font-size: 16px;
        color: #777;
        text-align: center;
        margin-bottom: 20px;
      }
      .about {
        width: 100%;
        background-color: #f5f5f5;
        padding: 50px;
  
        .detali {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          .aboutImgWeb {
            width: 340px;
            height: 250px;
            margin-right: 30px;
          }
          .info {
            width: 450px;
            text-indent: 2em;
            font-size: 14px;
            color: #333;
            line-height: 24px;
          }
        }
      }
      .fuwu {
        width: 100%;
        padding: 50px;
        .fwcontent {
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          .fwItem {
            width: 340px;
            height: 240px;
            margin-right: 30px;
            position: relative;
            overflow: hidden;
            img {
              width: 100%;
              height: 100%;
              transition: 2s;
            }
            img:hover {
              transform: scale(1.2);
            }
            .fwInfo {
              width: 100%;
              height: 100%;
              position: absolute;
              top: 0;
              left: 0;
              display: flex;
              justify-content: center;
              align-items: center;
              flex-direction: column;
              color: #fff;
              .main {
                width: 100%;
                font-size: 24px;
                text-align: center;
              }
              .second {
                width: 160px;
                font-size: 14px;
                text-align: center;
              }
            }
          }
          .fwItem:hover img {
            transform: scale(1.2);
          }
        }
      }
      .youxuan {
        width: 1500px;
        padding: 50px;
        margin: 0 auto;
        .fwcontent {
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-wrap: wrap;
          .fwItem {
            width: 250px;
            height: 230px;
            margin: 0px 30px 30px 0;
            position: relative;
            overflow: hidden;
            img {
              width: 100%;
              height: 100%;
              transition: 2s;
            }
            img:hover {
              transform: scale(1.2);
            }
            .fwInfo {
              width: 100%;
              height: 100%;
              position: absolute;
              top: 0;
              left: 0;
              display: flex;
              justify-content: center;
              align-items: center;
              flex-direction: column;
              color: #fff;
              .main {
                width: 100%;
                font-size: 24px;
                text-align: center;
              }
              .second {
                width: 160px;
                font-size: 14px;
                text-align: center;
              }
            }
          }
          .fwItem:hover img {
            transform: scale(1.2);
          }
        }
      }
      .shengri {
        width: 100%;
        padding: 50px;
        background: #f5f5f5;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        img {
          width: 1100px;
          height: 330px;
        }
      }
      .moviebg {
        width: 100%;
        img {
          width: 100%;
        }
      }
      .hezuo {
        width: 100%;
        padding: 50px;
        background: #fff;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        img {
          width: 1100px;
        }
      }
      .youshi {
        width: 100%;
        img {
          width: 100%;
        }
      }
      .footer {
        width: 100%;
        height: 120px;
        padding: 0px 50px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #333;
        font-size: 14px;
        box-sizing: border-box;
        margin-top: 50px;
        padding-bottom: 10px;
  
        .left {
          line-height: 24px;
        }
        .right {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .kefu {
            line-height: 24px;
            margin-right: 50px;
            .info {
              color: #666;
            }
            .tel {
              font-size: 24px;
            }
          }
          .qrCode {
            width: 90px;
            height: 90px;
            img {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }
    .gototop {
      width: 40px;
      height: 40px;
      position: fixed;
      bottom: 80px;
      right: 10px;
      .back-top {
        width: 40px;
        height: 40px;
        font-size: 20px;
        font-weight: bolder;
        box-shadow: inset 0 0 5px #4182ff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: auto;
      }
    }
  }
  a{
    text-decoration: underline;
    color: #4182ff;
  }
  .investment{
    font-size: 24px;
    background: linear-gradient(to right, #65A5F8, #456BF5);
    -webkit-background-clip: text;
    color: transparent;
    margin-right: 10px;
    cursor: pointer;
  }
  
  .floating-window {
    position: fixed;
    background-color: #fff;
    top: 10%;
    left: 10%;
    width: 80%;
    height: 80%;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    border-radius: 8px;
    overflow: hidden;
    .close-btn1 {
      position: absolute;
      top: 0px;
      right: 0px;
      border: none;
      width: 30.5px;
      height: 30.5px;
      cursor: pointer;
      z-index: 99999;
    }
  }
  
  .webFloating{
    position: fixed;
    background-color: #fff;
    top: 10%;
    left: 25%;
    width: 50%;
    height: 80%;
  }
  
  
  .iframe {
    width: 100%;
    height: 100%;
  }
  
  .close-btn {
    position: absolute;
    top: 10px;
    // right: 46%;
    right: 10px;
    border: none;
    width: 30.5px;
    height: 30.5px;
    cursor: pointer;
  }
  
  
  .floatingBox{
    padding:16px 5%  0px 12%;
    background: linear-gradient(to bottom, #E8F1FF, #D1E2FF);
  
    
  
    .floatingImg{
      width: 181px;
      height: 135px;
      align-self: flex-end;
      position: absolute;
      right: 12%;
      bottom:0;
    }
  }
  
  .floatingTitleBox{
    margin-bottom: 15px;
    margin-top: 2%;
    .floatingTitle{
      font-size: 24px;
      font-weight: bold;
      color: #3891E4;
      margin-bottom: 12px;
    }
    .floatingInfo{
      font-size: 18px;
      color:#333;
    }
  }
  
  .investmentPopup{
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    border-radius: 8px;
    overflow: hidden;
    background: #fff;
  }
  .webPopup{
    width: 50%;
  }
  
  .investmentPopup-top{
    width: 100%;
    height: 150px;
    background: linear-gradient(to bottom, #E8F1FF, #D1E2FF);
    position: relative;
  
  
    .investmentPopup-img{
      width: 181px;
      height: 135px;
      position: absolute;
      right: 0%;
      bottom: 0;
    }
  }
  
  .investmentPopup-content{
    background: #fff;
    padding:16px 10%  30px 8%;
  }
  
  .investmentPopup-goBtn{
    text-align: center;
    font-size: 20px;
    color: #fff;
    background: #0067fc;
    padding:10px 20px;
    border-radius: 8px;
    margin-top: 5%;
    cursor: pointer;
  }
  
  </style>
  