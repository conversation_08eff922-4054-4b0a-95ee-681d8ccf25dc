
* {
    padding: 0px;
    margin: 0px;
    box-sizing: border-box;
}
html{
    width:100% ;
    height:100% ; 
}
body {
    
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizeLegibility;
    font-family: Arial, Helvetica, sans-serif;
    width:100% ;
    height:100% ;

}

ul {
    list-style: none;
}

label {
    font-weight: 700;
}

html {
    box-sizing: border-box;
}

#app {
    height: 100%;
}

*,
*:before,
*:after {
    box-sizing: inherit;
}

a:focus,
a:active {
    outline: none;
}

a,
a:focus,
a:hover {
    cursor: pointer;
    color: inherit;
    text-decoration: none;
}

div:focus {
    outline: none;
}

textarea {
    outline-color: #cdcdcd;
}